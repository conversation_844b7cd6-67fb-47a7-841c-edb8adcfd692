# Qwen Solve API 多线程测试脚本

这是一个用于测试 POST /api/v1/solve 图片解题接口的Go多线程压力测试工具。

## 功能特点

- ✅ 10个并发线程同时请求
- ✅ 有序请求1000次（从ca0001.jpg到ca1000.jpg）
- ✅ 实时显示请求结果和进度
- ✅ 详细的统计信息（成功率、响应时间、QPS等）
- ✅ 错误处理和重试机制
- ✅ 支持自定义配置

## 文件说明

### 1. `test_solve_api.go` - 完整版测试脚本
- 功能最全面，包含详细的统计和分析
- 支持实时进度显示
- 包含性能建议和优化提示

### 2. `simple_test.go` - 简化版测试脚本  
- 代码更简洁，易于理解和修改
- 基本的并发测试功能
- 适合快速测试

## 使用方法

### 1. 修改配置

在脚本中修改以下配置项：

```go
const (
    API_URL        = "http://localhost:8080/api/v1/solve"  // 修改为你的API地址
    WORKERS        = 10                                    // 并发线程数
    TOTAL_REQUESTS = 1000                                  // 总请求数
    BASE_IMAGE_URL = "http://img.igmdns.com/img/ca"       // 图片URL前缀
)
```

### 2. 运行测试

#### 方式一：直接运行
```bash
# 运行完整版
go run test_solve_api.go

# 或运行简化版
go run simple_test.go
```

#### 方式二：编译后运行
```bash
# 编译
go build -o test_api test_solve_api.go

# 运行
./test_api
```

### 3. 查看结果

脚本会实时显示：
- 每个请求的成功/失败状态
- 响应时间
- 进度统计
- 最终汇总报告

## 测试场景

脚本会按顺序测试以下图片URL：
- http://img.igmdns.com/img/ca0001.jpg
- http://img.igmdns.com/img/ca0002.jpg
- ...
- http://img.igmdns.com/img/ca1000.jpg

## 输出示例

```
=== Qwen Solve API 压力测试 ===
API地址: http://localhost:8080/api/v1/solve
并发数: 10
请求数: 1000
开始测试...

实时结果:
✅ [1] 成功 - 1.234s
✅ [2] 成功 - 856ms
❌ [3] 失败 - API错误: HTTP 400, Code 1001, Message: 图片不存在 (耗时: 234ms)

[进度] 100/1000 完成 (成功: 95, 失败: 5)

=== 测试完成 ===
总耗时: 2m30s
成功: 950, 失败: 50
成功率: 95.00%
QPS: 6.67
响应时间 - 最小: 123ms, 最大: 5.678s, 平均: 1.234s
```

## 配置说明

### API配置
- `API_URL`: API接口地址，根据你的服务器地址修改
- `WORKERS`: 并发线程数，建议10-50之间
- `TOTAL_REQUESTS`: 总请求数，可根据需要调整
- `REQUEST_TIMEOUT`: 请求超时时间，默认60秒

### 图片URL配置
- `BASE_IMAGE_URL`: 图片URL前缀
- 脚本会自动生成 ca0001.jpg 到 ca1000.jpg 的URL

## 注意事项

1. **服务器地址**: 确保修改 `API_URL` 为正确的服务器地址
2. **网络环境**: 确保能够访问图片URL和API服务器
3. **并发限制**: 根据服务器性能调整并发数，避免过载
4. **超时设置**: 根据API响应时间调整超时设置
5. **错误处理**: 脚本会记录所有错误，便于问题排查

## 性能指标说明

- **QPS**: 每秒处理的请求数
- **成功率**: 成功请求占总请求的百分比
- **响应时间**: 包括最小、最大、平均响应时间
- **并发效率**: 多线程处理的效率

## 故障排查

### 常见错误
1. **连接失败**: 检查API服务器是否启动，地址是否正确
2. **超时**: 增加超时时间或检查网络连接
3. **图片不存在**: 确认图片URL是否可访问
4. **API错误**: 查看具体错误码和消息

### 调试建议
1. 先用少量请求测试（如10个）
2. 检查单个请求是否正常
3. 逐步增加并发数和请求数
4. 监控服务器资源使用情况

## 扩展功能

可以根据需要扩展以下功能：
- 支持不同类型的图片URL
- 添加请求头自定义
- 支持认证token
- 结果导出到文件
- 图形化结果展示
