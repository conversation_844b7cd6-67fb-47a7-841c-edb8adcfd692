package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"sync"
	"sync/atomic"
	"time"
)

// SolveImageRequest 图片解题请求结构体
type SolveImageRequest struct {
	ImageURL string `json:"image_url"`
}

// APIResponse 统一API响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	TraceID string      `json:"trace_id,omitempty"`
}

// SolveImageResponse 图片解题响应结构体
type SolveImageResponse struct {
	Questions []interface{} `json:"questions"`
	RequestID string        `json:"request_id"`
	Cached    bool          `json:"cached"`
	Source    string        `json:"source"`
}

// TestTask 测试任务结构体
type TestTask struct {
	ID       int
	ImageURL string
}

// TestResult 测试结果结构体
type TestResult struct {
	TaskID       int
	Success      bool
	StatusCode   int
	ResponseTime time.Duration
	Error        string
	Response     *APIResponse
}

// Statistics 统计信息结构体
type Statistics struct {
	TotalRequests   int64
	SuccessRequests int64
	FailedRequests  int64
	TotalTime       time.Duration
	MinTime         time.Duration
	MaxTime         time.Duration
	mutex           sync.RWMutex
}

// 全局统计
var stats = &Statistics{
	MinTime: time.Hour, // 初始化为一个很大的值
}

// 配置常量
const (
	API_BASE_URL    = "http://localhost:8080"  // 根据实际情况修改
	API_ENDPOINT    = "/api/v1/solve"
	WORKER_COUNT    = 10                       // 10个并发线程
	TOTAL_REQUESTS  = 1000                     // 总请求数
	IMAGE_URL_BASE  = "http://img.igmdns.com/img/ca"
	REQUEST_TIMEOUT = 60 * time.Second         // 请求超时时间
)

func main() {
	fmt.Println("=== Qwen Solve API 多线程测试工具 ===")
	fmt.Printf("API地址: %s%s\n", API_BASE_URL, API_ENDPOINT)
	fmt.Printf("并发线程数: %d\n", WORKER_COUNT)
	fmt.Printf("总请求数: %d\n", TOTAL_REQUESTS)
	fmt.Printf("图片URL模板: %s[0001-1000].jpg\n", IMAGE_URL_BASE)
	fmt.Println("开始测试...")
	fmt.Println()

	startTime := time.Now()

	// 创建任务通道和结果通道
	taskChan := make(chan TestTask, TOTAL_REQUESTS)
	resultChan := make(chan TestResult, TOTAL_REQUESTS)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < WORKER_COUNT; i++ {
		wg.Add(1)
		go worker(i+1, taskChan, resultChan, &wg)
	}

	// 启动结果收集协程
	go collectResults(resultChan)

	// 生成并发送任务（有序）
	go func() {
		defer close(taskChan)
		for i := 1; i <= TOTAL_REQUESTS; i++ {
			imageURL := fmt.Sprintf("%s%04d.jpg", IMAGE_URL_BASE, i)
			task := TestTask{
				ID:       i,
				ImageURL: imageURL,
			}
			taskChan <- task
		}
	}()

	// 等待所有工作协程完成
	wg.Wait()
	close(resultChan)

	// 等待结果收集完成
	time.Sleep(1 * time.Second)

	endTime := time.Now()
	totalDuration := endTime.Sub(startTime)

	// 打印最终统计
	printFinalStatistics(totalDuration)
}

// worker 工作协程
func worker(workerID int, taskChan <-chan TestTask, resultChan chan<- TestResult, wg *sync.WaitGroup) {
	defer wg.Done()

	client := &http.Client{
		Timeout: REQUEST_TIMEOUT,
	}

	fmt.Printf("[Worker %d] 启动\n", workerID)

	for task := range taskChan {
		result := performRequest(client, task, workerID)
		resultChan <- result
	}

	fmt.Printf("[Worker %d] 完成\n", workerID)
}

// performRequest 执行HTTP请求
func performRequest(client *http.Client, task TestTask, workerID int) TestResult {
	startTime := time.Now()

	// 构造请求体
	requestBody := SolveImageRequest{
		ImageURL: task.ImageURL,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return TestResult{
			TaskID:       task.ID,
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Sprintf("JSON编码失败: %v", err),
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", API_BASE_URL+API_ENDPOINT, bytes.NewBuffer(jsonData))
	if err != nil {
		return TestResult{
			TaskID:       task.ID,
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Sprintf("创建请求失败: %v", err),
		}
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", fmt.Sprintf("QwenSolveTestClient/1.0 Worker-%d", workerID))

	// 发送请求
	resp, err := client.Do(req)
	responseTime := time.Since(startTime)

	if err != nil {
		return TestResult{
			TaskID:       task.ID,
			Success:      false,
			StatusCode:   0,
			ResponseTime: responseTime,
			Error:        fmt.Sprintf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TaskID:       task.ID,
			Success:      false,
			StatusCode:   resp.StatusCode,
			ResponseTime: responseTime,
			Error:        fmt.Sprintf("读取响应失败: %v", err),
		}
	}

	// 解析响应
	var apiResponse APIResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return TestResult{
			TaskID:       task.ID,
			Success:      resp.StatusCode == 200,
			StatusCode:   resp.StatusCode,
			ResponseTime: responseTime,
			Error:        fmt.Sprintf("解析响应失败: %v", err),
		}
	}

	success := resp.StatusCode == 200 && apiResponse.Code == 0

	result := TestResult{
		TaskID:       task.ID,
		Success:      success,
		StatusCode:   resp.StatusCode,
		ResponseTime: responseTime,
		Response:     &apiResponse,
	}

	if !success {
		result.Error = fmt.Sprintf("API错误: Code=%d, Message=%s", apiResponse.Code, apiResponse.Message)
	}

	return result
}

// collectResults 收集测试结果
func collectResults(resultChan <-chan TestResult) {
	var completedCount int64
	var lastPrintTime time.Time

	for result := range resultChan {
		// 更新统计信息
		updateStatistics(result)

		completedCount++

		// 每100个请求或每5秒打印一次进度
		now := time.Now()
		if completedCount%100 == 0 || now.Sub(lastPrintTime) >= 5*time.Second {
			printProgress(completedCount)
			lastPrintTime = now
		}

		// 如果请求失败，打印错误信息
		if !result.Success {
			fmt.Printf("[错误] 任务 %d 失败: %s (耗时: %v)\n",
				result.TaskID, result.Error, result.ResponseTime)
		}

		// 打印成功的请求详情（仅前10个和后10个）
		if result.Success && (result.TaskID <= 10 || result.TaskID > TOTAL_REQUESTS-10) {
			source := "unknown"
			cached := false
			if result.Response != nil && result.Response.Data != nil {
				if data, ok := result.Response.Data.(map[string]interface{}); ok {
					if s, exists := data["source"]; exists {
						source = fmt.Sprintf("%v", s)
					}
					if c, exists := data["cached"]; exists {
						cached = fmt.Sprintf("%v", c) == "true"
					}
				}
			}
			fmt.Printf("[成功] 任务 %d: %s (缓存: %v, 来源: %s, 耗时: %v)\n",
				result.TaskID, result.TaskID <= 10 ? "开始" : "结束", cached, source, result.ResponseTime)
		}
	}
}

// updateStatistics 更新统计信息
func updateStatistics(result TestResult) {
	stats.mutex.Lock()
	defer stats.mutex.Unlock()

	atomic.AddInt64(&stats.TotalRequests, 1)

	if result.Success {
		atomic.AddInt64(&stats.SuccessRequests, 1)
	} else {
		atomic.AddInt64(&stats.FailedRequests, 1)
	}

	stats.TotalTime += result.ResponseTime

	if result.ResponseTime < stats.MinTime {
		stats.MinTime = result.ResponseTime
	}

	if result.ResponseTime > stats.MaxTime {
		stats.MaxTime = result.ResponseTime
	}
}

// printProgress 打印进度信息
func printProgress(completed int64) {
	stats.mutex.RLock()
	successRate := float64(stats.SuccessRequests) / float64(stats.TotalRequests) * 100
	avgTime := stats.TotalTime / time.Duration(stats.TotalRequests)
	stats.mutex.RUnlock()

	fmt.Printf("[进度] 已完成: %d/%d (%.1f%%), 成功率: %.1f%%, 平均耗时: %v\n",
		completed, TOTAL_REQUESTS, float64(completed)/float64(TOTAL_REQUESTS)*100,
		successRate, avgTime)
}

// printFinalStatistics 打印最终统计信息
func printFinalStatistics(totalDuration time.Duration) {
	stats.mutex.RLock()
	defer stats.mutex.RUnlock()

	fmt.Println()
	fmt.Println("=== 测试完成 ===")
	fmt.Printf("总耗时: %v\n", totalDuration)
	fmt.Printf("总请求数: %d\n", stats.TotalRequests)
	fmt.Printf("成功请求数: %d\n", stats.SuccessRequests)
	fmt.Printf("失败请求数: %d\n", stats.FailedRequests)
	fmt.Printf("成功率: %.2f%%\n", float64(stats.SuccessRequests)/float64(stats.TotalRequests)*100)
	fmt.Println()

	if stats.TotalRequests > 0 {
		avgTime := stats.TotalTime / time.Duration(stats.TotalRequests)
		fmt.Printf("响应时间统计:\n")
		fmt.Printf("  最小耗时: %v\n", stats.MinTime)
		fmt.Printf("  最大耗时: %v\n", stats.MaxTime)
		fmt.Printf("  平均耗时: %v\n", avgTime)
		fmt.Println()
	}

	// 计算QPS
	qps := float64(stats.TotalRequests) / totalDuration.Seconds()
	fmt.Printf("性能指标:\n")
	fmt.Printf("  QPS (每秒请求数): %.2f\n", qps)
	fmt.Printf("  并发数: %d\n", WORKER_COUNT)
	fmt.Printf("  平均并发处理时间: %v\n", totalDuration/time.Duration(WORKER_COUNT))

	// 建议
	fmt.Println()
	fmt.Println("=== 测试建议 ===")
	if stats.SuccessRequests == stats.TotalRequests {
		fmt.Println("✅ 所有请求都成功了！API运行正常。")
	} else if float64(stats.SuccessRequests)/float64(stats.TotalRequests) >= 0.95 {
		fmt.Println("⚠️  大部分请求成功，但有少量失败，建议检查失败原因。")
	} else {
		fmt.Println("❌ 失败率较高，建议检查API服务状态和网络连接。")
	}

	avgTime := stats.TotalTime / time.Duration(stats.TotalRequests)
	if avgTime < 1*time.Second {
		fmt.Println("✅ 响应时间良好。")
	} else if avgTime < 5*time.Second {
		fmt.Println("⚠️  响应时间较慢，建议优化API性能。")
	} else {
		fmt.Println("❌ 响应时间过慢，建议检查API性能瓶颈。")
	}

	if qps > 10 {
		fmt.Println("✅ QPS表现良好。")
	} else {
		fmt.Println("⚠️  QPS较低，可能存在性能瓶颈。")
	}
}
