package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

// 配置结构体
type Config struct {
	APIURL       string
	Workers      int
	TotalReqs    int
	BaseImageURL string
	Timeout      time.Duration
	StartNum     int
	EndNum       int
}

// 请求和响应结构体
type SolveRequest struct {
	ImageURL string `json:"image_url"`
}

type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type TestResult struct {
	ID           int
	Success      bool
	ResponseTime time.Duration
	StatusCode   int
	Error        string
}

func main() {
	// 命令行参数
	var config Config
	flag.StringVar(&config.APIURL, "url", "http://localhost:8080/api/v1/solve", "API接口地址")
	flag.IntVar(&config.Workers, "workers", 10, "并发线程数")
	flag.IntVar(&config.TotalReqs, "requests", 1000, "总请求数")
	flag.StringVar(&config.BaseImageURL, "base-url", "http://img.igmdns.com/img/ca", "图片URL前缀")
	flag.DurationVar(&config.Timeout, "timeout", 60*time.Second, "请求超时时间")
	flag.IntVar(&config.StartNum, "start", 1, "开始编号")
	flag.IntVar(&config.EndNum, "end", 1000, "结束编号")
	flag.Parse()

	// 调整总请求数
	if config.EndNum > config.StartNum {
		config.TotalReqs = config.EndNum - config.StartNum + 1
	}

	fmt.Println("=== Qwen Solve API 可配置测试工具 ===")
	fmt.Printf("API地址: %s\n", config.APIURL)
	fmt.Printf("并发数: %d\n", config.Workers)
	fmt.Printf("请求数: %d\n", config.TotalReqs)
	fmt.Printf("图片范围: %s%04d.jpg ~ %s%04d.jpg\n", 
		config.BaseImageURL, config.StartNum, config.BaseImageURL, config.EndNum)
	fmt.Printf("超时时间: %v\n", config.Timeout)
	fmt.Println("开始测试...")

	runTest(config)
}

func runTest(config Config) {
	startTime := time.Now()

	// 创建任务队列
	tasks := make(chan int, config.TotalReqs)
	results := make(chan TestResult, config.TotalReqs)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < config.Workers; i++ {
		wg.Add(1)
		go worker(i+1, tasks, results, &wg, config)
	}

	// 发送任务
	go func() {
		defer close(tasks)
		for i := config.StartNum; i <= config.EndNum; i++ {
			tasks <- i
		}
	}()

	// 收集结果
	go func() {
		wg.Wait()
		close(results)
	}()

	// 统计结果
	var successCount, failCount int
	var totalTime time.Duration
	var minTime, maxTime time.Duration = time.Hour, 0
	var errorStats = make(map[string]int)

	fmt.Println("\n实时结果:")
	for result := range results {
		if result.Success {
			successCount++
			fmt.Printf("✅ [%d] 成功 - %v\n", result.ID, result.ResponseTime)
		} else {
			failCount++
			fmt.Printf("❌ [%d] 失败 - %s (耗时: %v)\n", result.ID, result.Error, result.ResponseTime)
			// 统计错误类型
			errorStats[result.Error]++
		}

		totalTime += result.ResponseTime
		if result.ResponseTime < minTime {
			minTime = result.ResponseTime
		}
		if result.ResponseTime > maxTime {
			maxTime = result.ResponseTime
		}

		// 每50个请求显示进度
		completed := successCount + failCount
		if completed%50 == 0 {
			fmt.Printf("\n[进度] %d/%d 完成 (成功: %d, 失败: %d)\n\n", 
				completed, config.TotalReqs, successCount, failCount)
		}
	}

	endTime := time.Now()
	totalDuration := endTime.Sub(startTime)

	// 打印详细统计
	printDetailedStats(config, successCount, failCount, totalTime, minTime, maxTime, totalDuration, errorStats)
}

func worker(workerID int, tasks <-chan int, results chan<- TestResult, wg *sync.WaitGroup, config Config) {
	defer wg.Done()

	client := &http.Client{
		Timeout: config.Timeout,
	}

	for taskID := range tasks {
		result := doRequest(client, taskID, workerID, config)
		results <- result
	}
}

func doRequest(client *http.Client, taskID, workerID int, config Config) TestResult {
	startTime := time.Now()

	// 构造图片URL
	imageURL := fmt.Sprintf("%s%04d.jpg", config.BaseImageURL, taskID)

	// 构造请求体
	reqBody := SolveRequest{
		ImageURL: imageURL,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return TestResult{
			ID:           taskID,
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Sprintf("JSON编码失败: %v", err),
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", config.APIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return TestResult{
			ID:           taskID,
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Sprintf("创建请求失败: %v", err),
		}
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", fmt.Sprintf("QwenTestClient/1.0 Worker-%d", workerID))

	// 发送请求
	resp, err := client.Do(req)
	responseTime := time.Since(startTime)

	if err != nil {
		return TestResult{
			ID:           taskID,
			Success:      false,
			ResponseTime: responseTime,
			StatusCode:   0,
			Error:        fmt.Sprintf("网络错误: %v", err),
		}
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			ID:           taskID,
			Success:      false,
			ResponseTime: responseTime,
			StatusCode:   resp.StatusCode,
			Error:        fmt.Sprintf("读取响应失败: %v", err),
		}
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return TestResult{
			ID:           taskID,
			Success:      resp.StatusCode == 200,
			ResponseTime: responseTime,
			StatusCode:   resp.StatusCode,
			Error:        fmt.Sprintf("解析响应失败: %v", err),
		}
	}

	// 判断成功
	success := resp.StatusCode == 200 && apiResp.Code == 0

	result := TestResult{
		ID:           taskID,
		Success:      success,
		ResponseTime: responseTime,
		StatusCode:   resp.StatusCode,
	}

	if !success {
		if apiResp.Code != 0 {
			result.Error = fmt.Sprintf("API错误[%d]: %s", apiResp.Code, apiResp.Message)
		} else {
			result.Error = fmt.Sprintf("HTTP错误: %d", resp.StatusCode)
		}
	}

	return result
}

func printDetailedStats(config Config, successCount, failCount int, totalTime, minTime, maxTime, totalDuration time.Duration, errorStats map[string]int) {
	fmt.Println("\n" + "="*60)
	fmt.Println("测试完成 - 详细统计报告")
	fmt.Println("="*60)
	
	// 基本统计
	fmt.Printf("📊 基本统计:\n")
	fmt.Printf("   总耗时: %v\n", totalDuration)
	fmt.Printf("   成功: %d, 失败: %d, 总计: %d\n", successCount, failCount, config.TotalReqs)
	fmt.Printf("   成功率: %.2f%%\n", float64(successCount)/float64(config.TotalReqs)*100)
	fmt.Printf("   QPS: %.2f 请求/秒\n", float64(config.TotalReqs)/totalDuration.Seconds())
	fmt.Println()

	// 响应时间统计
	if config.TotalReqs > 0 {
		avgTime := totalTime / time.Duration(config.TotalReqs)
		fmt.Printf("⏱️  响应时间统计:\n")
		fmt.Printf("   最小: %v\n", minTime)
		fmt.Printf("   最大: %v\n", maxTime)
		fmt.Printf("   平均: %v\n", avgTime)
		fmt.Println()
	}

	// 错误统计
	if len(errorStats) > 0 {
		fmt.Printf("❌ 错误统计:\n")
		for errMsg, count := range errorStats {
			fmt.Printf("   %s: %d次\n", errMsg, count)
		}
		fmt.Println()
	}

	// 性能评估
	fmt.Printf("📈 性能评估:\n")
	successRate := float64(successCount) / float64(config.TotalReqs) * 100
	qps := float64(config.TotalReqs) / totalDuration.Seconds()
	avgTime := totalTime / time.Duration(config.TotalReqs)

	if successRate >= 99 {
		fmt.Printf("   ✅ 成功率优秀 (%.2f%%)\n", successRate)
	} else if successRate >= 95 {
		fmt.Printf("   ⚠️  成功率良好 (%.2f%%)\n", successRate)
	} else {
		fmt.Printf("   ❌ 成功率需要改进 (%.2f%%)\n", successRate)
	}

	if qps >= 20 {
		fmt.Printf("   ✅ QPS表现优秀 (%.2f)\n", qps)
	} else if qps >= 10 {
		fmt.Printf("   ⚠️  QPS表现良好 (%.2f)\n", qps)
	} else {
		fmt.Printf("   ❌ QPS需要优化 (%.2f)\n", qps)
	}

	if avgTime <= 1*time.Second {
		fmt.Printf("   ✅ 响应时间优秀 (%v)\n", avgTime)
	} else if avgTime <= 3*time.Second {
		fmt.Printf("   ⚠️  响应时间良好 (%v)\n", avgTime)
	} else {
		fmt.Printf("   ❌ 响应时间需要优化 (%v)\n", avgTime)
	}

	fmt.Println("\n" + "="*60)
}
