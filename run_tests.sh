#!/bin/bash

# Qwen Solve API 测试脚本集合
# 使用方法: ./run_tests.sh [test_type]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
API_URL="http://localhost:8080/api/v1/solve"
WORKERS=10
REQUESTS=1000
BASE_URL="http://img.igmdns.com/img/ca"

echo -e "${BLUE}=== Qwen Solve API 测试工具集 ===${NC}"
echo

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: 未找到Go环境，请先安装Go${NC}"
    exit 1
fi

# 显示使用帮助
show_help() {
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  ./run_tests.sh [选项]"
    echo
    echo -e "${YELLOW}选项:${NC}"
    echo "  simple     - 运行简化版测试 (默认)"
    echo "  full       - 运行完整版测试"
    echo "  config     - 运行可配置测试"
    echo "  quick      - 快速测试 (10个请求)"
    echo "  stress     - 压力测试 (5000个请求)"
    echo "  help       - 显示此帮助信息"
    echo
    echo -e "${YELLOW}示例:${NC}"
    echo "  ./run_tests.sh simple"
    echo "  ./run_tests.sh config --url http://your-api.com/api/v1/solve"
    echo "  ./run_tests.sh quick"
    echo
}

# 检查API连通性
check_api() {
    echo -e "${BLUE}检查API连通性...${NC}"
    
    # 检查健康检查接口
    HEALTH_URL=$(echo $API_URL | sed 's|/api/v1/solve|/health|')
    
    if curl -s --connect-timeout 5 "$HEALTH_URL" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API服务正常${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  无法连接到API服务，将继续测试...${NC}"
        return 1
    fi
}

# 运行简化版测试
run_simple_test() {
    echo -e "${BLUE}运行简化版测试...${NC}"
    echo "配置: $WORKERS 并发, $REQUESTS 请求"
    echo
    
    if [ ! -f "simple_test.go" ]; then
        echo -e "${RED}错误: 找不到 simple_test.go 文件${NC}"
        exit 1
    fi
    
    go run simple_test.go
}

# 运行完整版测试
run_full_test() {
    echo -e "${BLUE}运行完整版测试...${NC}"
    echo "配置: $WORKERS 并发, $REQUESTS 请求"
    echo
    
    if [ ! -f "test_solve_api.go" ]; then
        echo -e "${RED}错误: 找不到 test_solve_api.go 文件${NC}"
        exit 1
    fi
    
    go run test_solve_api.go
}

# 运行可配置测试
run_config_test() {
    echo -e "${BLUE}运行可配置测试...${NC}"
    
    if [ ! -f "config_test.go" ]; then
        echo -e "${RED}错误: 找不到 config_test.go 文件${NC}"
        exit 1
    fi
    
    # 传递额外的命令行参数
    shift # 移除第一个参数 (test_type)
    go run config_test.go "$@"
}

# 快速测试
run_quick_test() {
    echo -e "${BLUE}运行快速测试 (10个请求)...${NC}"
    
    if [ ! -f "config_test.go" ]; then
        echo -e "${RED}错误: 找不到 config_test.go 文件${NC}"
        exit 1
    fi
    
    go run config_test.go -requests 10 -workers 5 -start 1 -end 10
}

# 压力测试
run_stress_test() {
    echo -e "${BLUE}运行压力测试 (5000个请求)...${NC}"
    echo -e "${YELLOW}警告: 这将产生大量请求，请确保服务器能够承受${NC}"
    
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "测试已取消"
        exit 0
    fi
    
    if [ ! -f "config_test.go" ]; then
        echo -e "${RED}错误: 找不到 config_test.go 文件${NC}"
        exit 1
    fi
    
    go run config_test.go -requests 5000 -workers 20 -start 1 -end 5000
}

# 编译所有测试程序
compile_tests() {
    echo -e "${BLUE}编译测试程序...${NC}"
    
    if [ -f "simple_test.go" ]; then
        echo "编译 simple_test..."
        go build -o simple_test simple_test.go
        echo -e "${GREEN}✅ simple_test 编译完成${NC}"
    fi
    
    if [ -f "test_solve_api.go" ]; then
        echo "编译 test_solve_api..."
        go build -o test_solve_api test_solve_api.go
        echo -e "${GREEN}✅ test_solve_api 编译完成${NC}"
    fi
    
    if [ -f "config_test.go" ]; then
        echo "编译 config_test..."
        go build -o config_test config_test.go
        echo -e "${GREEN}✅ config_test 编译完成${NC}"
    fi
    
    echo -e "${GREEN}所有程序编译完成！${NC}"
    echo
    echo "现在可以直接运行:"
    echo "  ./simple_test"
    echo "  ./test_solve_api"
    echo "  ./config_test [参数]"
}

# 清理编译文件
clean_builds() {
    echo -e "${BLUE}清理编译文件...${NC}"
    rm -f simple_test test_solve_api config_test
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 主逻辑
case "${1:-simple}" in
    "simple")
        check_api
        run_simple_test
        ;;
    "full")
        check_api
        run_full_test
        ;;
    "config")
        check_api
        run_config_test "$@"
        ;;
    "quick")
        check_api
        run_quick_test
        ;;
    "stress")
        check_api
        run_stress_test
        ;;
    "compile")
        compile_tests
        ;;
    "clean")
        clean_builds
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo -e "${RED}错误: 未知选项 '$1'${NC}"
        echo
        show_help
        exit 1
        ;;
esac
