# Qwen Solve API 测试工具使用指南

## 📋 概述

这是一套完整的Go多线程API测试工具，专门用于测试POST /api/v1/solve图片解题接口。包含3个不同复杂度的测试脚本和1个便捷的运行脚本。

## 🚀 快速开始

### 1. 检查环境
确保已安装Go 1.18+：
```bash
go version
```

### 2. 修改API地址
在脚本中修改API地址为你的实际服务器地址：
```go
const API_URL = "http://your-server:8080/api/v1/solve"
```

### 3. 运行测试
```bash
# 最简单的方式 - 运行简化版测试
./run_tests.sh simple

# 或者直接运行
go run simple_test.go
```

## 📁 文件说明

| 文件 | 描述 | 适用场景 |
|------|------|----------|
| `simple_test.go` | 简化版测试脚本 | 快速测试，代码简洁 |
| `test_solve_api.go` | 完整版测试脚本 | 详细统计，功能全面 |
| `config_test.go` | 可配置测试脚本 | 灵活配置，命令行参数 |
| `run_tests.sh` | 便捷运行脚本 | 一键运行各种测试 |

## 🛠️ 使用方法

### 方法一：使用便捷脚本（推荐）

```bash
# 简化版测试（默认）
./run_tests.sh simple

# 完整版测试
./run_tests.sh full

# 可配置测试
./run_tests.sh config

# 快速测试（10个请求）
./run_tests.sh quick

# 压力测试（5000个请求）
./run_tests.sh stress

# 编译所有程序
./run_tests.sh compile

# 显示帮助
./run_tests.sh help
```

### 方法二：直接运行Go脚本

```bash
# 简化版
go run simple_test.go

# 完整版
go run test_solve_api.go

# 可配置版（支持命令行参数）
go run config_test.go -url http://localhost:8080/api/v1/solve -workers 10 -requests 100
```

### 方法三：编译后运行

```bash
# 编译
go build -o test_api simple_test.go

# 运行
./test_api
```

## ⚙️ 配置参数

### 可配置测试脚本参数

```bash
go run config_test.go [参数]

参数说明:
  -url string        API接口地址 (默认: http://localhost:8080/api/v1/solve)
  -workers int       并发线程数 (默认: 10)
  -requests int      总请求数 (默认: 1000)
  -base-url string   图片URL前缀 (默认: http://img.igmdns.com/img/ca)
  -timeout duration  请求超时时间 (默认: 60s)
  -start int         开始编号 (默认: 1)
  -end int           结束编号 (默认: 1000)
```

### 使用示例

```bash
# 测试特定范围的图片
go run config_test.go -start 1 -end 100 -workers 5

# 使用不同的API地址
go run config_test.go -url http://*************:8080/api/v1/solve

# 高并发测试
go run config_test.go -workers 50 -requests 5000

# 设置超时时间
go run config_test.go -timeout 30s
```

## 📊 输出说明

### 实时输出
```
✅ [1] 成功 - 1.234s
❌ [3] 失败 - API错误[1001]: 图片不存在 (耗时: 234ms)
[进度] 100/1000 完成 (成功: 95, 失败: 5)
```

### 最终统计
```
=== 测试完成 ===
总耗时: 2m30s
成功: 950, 失败: 50
成功率: 95.00%
QPS: 6.67
响应时间 - 最小: 123ms, 最大: 5.678s, 平均: 1.234s
```

## 🎯 测试场景

### 1. 功能测试
```bash
# 测试少量请求确保API正常
./run_tests.sh quick
```

### 2. 性能测试
```bash
# 标准性能测试
./run_tests.sh simple

# 高并发测试
go run config_test.go -workers 20 -requests 2000
```

### 3. 压力测试
```bash
# 大量请求压力测试
./run_tests.sh stress
```

### 4. 特定范围测试
```bash
# 测试特定图片编号范围
go run config_test.go -start 500 -end 600
```

## 🔧 故障排查

### 常见问题

1. **连接失败**
   ```
   ❌ [1] 失败 - 网络错误: dial tcp: connection refused
   ```
   - 检查API服务是否启动
   - 确认API地址是否正确
   - 检查防火墙设置

2. **图片不存在**
   ```
   ❌ [1] 失败 - API错误[1001]: 图片不存在,上传失败,请联系管理员处理！
   ```
   - 确认图片URL是否可访问
   - 检查图片编号范围是否正确

3. **超时错误**
   ```
   ❌ [1] 失败 - 网络错误: context deadline exceeded
   ```
   - 增加超时时间：`-timeout 120s`
   - 减少并发数：`-workers 5`

4. **API错误**
   ```
   ❌ [1] 失败 - API错误[1007]: 图片不标准，题目类型未正确识别
   ```
   - 检查图片质量和格式
   - 确认API服务配置正确

### 调试建议

1. **先小规模测试**
   ```bash
   go run config_test.go -requests 10 -workers 2
   ```

2. **检查单个请求**
   ```bash
   curl -X POST http://localhost:8080/api/v1/solve \
     -H "Content-Type: application/json" \
     -d '{"image_url":"http://img.igmdns.com/img/ca0001.jpg"}'
   ```

3. **监控服务器资源**
   - CPU使用率
   - 内存使用率
   - 网络连接数

## 📈 性能优化建议

### 客户端优化
- 根据服务器性能调整并发数
- 设置合适的超时时间
- 使用连接池复用连接

### 服务器优化
- 监控API响应时间
- 检查数据库连接池配置
- 优化Redis缓存策略

## 🔄 扩展功能

可以根据需要扩展以下功能：

1. **结果导出**
   - 导出CSV格式统计
   - 生成HTML报告

2. **实时监控**
   - WebSocket实时显示
   - 图形化界面

3. **自动化测试**
   - 集成到CI/CD流程
   - 定时性能测试

4. **多环境支持**
   - 配置文件管理
   - 环境变量支持

## 📞 技术支持

如果遇到问题，请检查：
1. Go版本是否兼容
2. 网络连接是否正常
3. API服务是否正常运行
4. 配置参数是否正确

建议在生产环境使用前，先在测试环境充分验证。
