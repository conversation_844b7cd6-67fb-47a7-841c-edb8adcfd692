package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

// 请求结构体
type SolveRequest struct {
	ImageURL string `json:"image_url"`
}

// 响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 测试结果
type TestResult struct {
	ID           int
	Success      bool
	ResponseTime time.Duration
	StatusCode   int
	Error        string
}

// 配置
const (
	API_URL        = "http://localhost:8080/api/v1/solve"  // 修改为你的API地址
	WORKERS        = 10                                    // 并发线程数
	TOTAL_REQUESTS = 1000                                  // 总请求数
	BASE_IMAGE_URL = "http://img.igmdns.com/img/ca"       // 图片URL前缀
)

func main() {
	fmt.Println("=== Qwen Solve API 压力测试 ===")
	fmt.Printf("API地址: %s\n", API_URL)
	fmt.Printf("并发数: %d\n", WORKERS)
	fmt.Printf("请求数: %d\n", TOTAL_REQUESTS)
	fmt.Println("开始测试...")

	startTime := time.Now()

	// 创建任务队列
	tasks := make(chan int, TOTAL_REQUESTS)
	results := make(chan TestResult, TOTAL_REQUESTS)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < WORKERS; i++ {
		wg.Add(1)
		go worker(i+1, tasks, results, &wg)
	}

	// 发送任务
	go func() {
		defer close(tasks)
		for i := 1; i <= TOTAL_REQUESTS; i++ {
			tasks <- i
		}
	}()

	// 收集结果
	go func() {
		wg.Wait()
		close(results)
	}()

	// 统计结果
	var successCount, failCount int
	var totalTime time.Duration
	var minTime, maxTime time.Duration = time.Hour, 0

	fmt.Println("\n实时结果:")
	for result := range results {
		if result.Success {
			successCount++
			fmt.Printf("✅ [%d] 成功 - %v\n", result.ID, result.ResponseTime)
		} else {
			failCount++
			fmt.Printf("❌ [%d] 失败 - %s (耗时: %v)\n", result.ID, result.Error, result.ResponseTime)
		}

		totalTime += result.ResponseTime
		if result.ResponseTime < minTime {
			minTime = result.ResponseTime
		}
		if result.ResponseTime > maxTime {
			maxTime = result.ResponseTime
		}

		// 每100个请求显示进度
		completed := successCount + failCount
		if completed%100 == 0 {
			fmt.Printf("\n[进度] %d/%d 完成 (成功: %d, 失败: %d)\n\n", 
				completed, TOTAL_REQUESTS, successCount, failCount)
		}
	}

	endTime := time.Now()
	totalDuration := endTime.Sub(startTime)

	// 打印最终统计
	fmt.Println("\n=== 测试完成 ===")
	fmt.Printf("总耗时: %v\n", totalDuration)
	fmt.Printf("成功: %d, 失败: %d\n", successCount, failCount)
	fmt.Printf("成功率: %.2f%%\n", float64(successCount)/float64(TOTAL_REQUESTS)*100)
	fmt.Printf("QPS: %.2f\n", float64(TOTAL_REQUESTS)/totalDuration.Seconds())
	
	if TOTAL_REQUESTS > 0 {
		avgTime := totalTime / time.Duration(TOTAL_REQUESTS)
		fmt.Printf("响应时间 - 最小: %v, 最大: %v, 平均: %v\n", minTime, maxTime, avgTime)
	}
}

// 工作协程
func worker(workerID int, tasks <-chan int, results chan<- TestResult, wg *sync.WaitGroup) {
	defer wg.Done()

	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	for taskID := range tasks {
		result := doRequest(client, taskID, workerID)
		results <- result
	}
}

// 执行HTTP请求
func doRequest(client *http.Client, taskID, workerID int) TestResult {
	startTime := time.Now()

	// 构造图片URL (格式: ca0001.jpg, ca0002.jpg, ...)
	imageURL := fmt.Sprintf("%s%04d.jpg", BASE_IMAGE_URL, taskID)

	// 构造请求体
	reqBody := SolveRequest{
		ImageURL: imageURL,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return TestResult{
			ID:           taskID,
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Sprintf("JSON编码失败: %v", err),
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", API_URL, bytes.NewBuffer(jsonData))
	if err != nil {
		return TestResult{
			ID:           taskID,
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Sprintf("创建请求失败: %v", err),
		}
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", fmt.Sprintf("TestClient-Worker%d", workerID))

	// 发送请求
	resp, err := client.Do(req)
	responseTime := time.Since(startTime)

	if err != nil {
		return TestResult{
			ID:           taskID,
			Success:      false,
			ResponseTime: responseTime,
			StatusCode:   0,
			Error:        fmt.Sprintf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			ID:           taskID,
			Success:      false,
			ResponseTime: responseTime,
			StatusCode:   resp.StatusCode,
			Error:        fmt.Sprintf("读取响应失败: %v", err),
		}
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return TestResult{
			ID:           taskID,
			Success:      resp.StatusCode == 200,
			ResponseTime: responseTime,
			StatusCode:   resp.StatusCode,
			Error:        fmt.Sprintf("解析响应失败: %v", err),
		}
	}

	// 判断成功
	success := resp.StatusCode == 200 && apiResp.Code == 0

	result := TestResult{
		ID:           taskID,
		Success:      success,
		ResponseTime: responseTime,
		StatusCode:   resp.StatusCode,
	}

	if !success {
		result.Error = fmt.Sprintf("API错误: HTTP %d, Code %d, Message: %s", 
			resp.StatusCode, apiResp.Code, apiResp.Message)
	}

	return result
}
